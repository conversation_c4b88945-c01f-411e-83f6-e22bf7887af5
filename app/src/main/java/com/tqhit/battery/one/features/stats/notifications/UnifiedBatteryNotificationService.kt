package com.tqhit.battery.one.features.stats.notifications

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.app.ForegroundServiceStartNotAllowedException
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import com.tqhit.battery.one.R
import com.tqhit.battery.one.activity.password.EnterPasswordActivity
// Legacy CheckChargeAlarmUseCase removed - alarm logic handled directly in service
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.utils.NotificationUtils
import com.tqhit.battery.one.utils.AntiThiefUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Unified battery notification service that uses CoreBatteryStatsProvider
 * as the single source of truth for battery data.
 *
 * This service now integrates with the modern CoreBatteryStatsService for
 * unified notifications and provides alarm functionality for charge targets.
 */
@AndroidEntryPoint
class UnifiedBatteryNotificationService : Service() {

    @Inject
    lateinit var coreBatteryStatsProvider: CoreBatteryStatsProvider

    // Legacy checkChargeAlarmUseCase injection removed - alarm logic handled directly

    @Inject
    lateinit var appRepository: AppRepository
    
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private var isMonitoring = false
    private var lastBatteryLevel = -1
    private var hasNotifiedTargetPercent = false
    private var hasNotifiedFullCharge = false
    private var wasCharging = false
    private var antiThiefHandler: android.os.Handler? = null
    private var antiThiefRunnable: Runnable? = null

    // Track previous status for alarm detection
    private var lastBatteryStatus: CoreBatteryStatus? = null
    private var isScreenOn = true  // Assume screen is on initially

    // CoreBatteryStatsService management
    private var coreBatteryStatsServiceStarted = false

    // Service state management
    private var isForegroundServiceActive = false
    private var isRunningInFallbackMode = false

    companion object {
        private const val TAG = "UnifiedBatteryNotificationService"
        private const val CHANNEL_ID = "unified_battery_monitor_channel"
        private const val CHANNEL_NAME = "Battery Alarm Service"
        private const val NOTIFICATION_ID = 2

        // Enhanced debug logging tags for structured monitoring
        private const val LIFECYCLE_TAG = "UnifiedBatteryService_Lifecycle"
        private const val FOREGROUND_TAG = "UnifiedBatteryService_Foreground"
        private const val FALLBACK_TAG = "UnifiedBatteryService_Fallback"
        private const val MONITORING_TAG = "UnifiedBatteryService_Monitoring"
        private const val ERROR_TAG = "UnifiedBatteryService_Error"

        // Adaptive notification update intervals for battery efficiency
        private const val NOTIFICATION_UPDATE_INTERVAL_CHARGING_MS = 15000L  // 15 seconds when charging
        private const val NOTIFICATION_UPDATE_INTERVAL_STABLE_MS = 45000L    // 45 seconds when stable
        private const val NOTIFICATION_UPDATE_INTERVAL_SCREEN_OFF_MS = 60000L // 1 minute when screen off

        private const val ANTI_THEFT_DELAY_MS = 5000L  // 5 seconds

        // Threshold for detecting significant battery changes
        private const val SIGNIFICANT_CHANGE_THRESHOLD_PERCENT = 1
        private const val SIGNIFICANT_CHANGE_THRESHOLD_CURRENT_MA = 100
    }
    
    override fun onCreate() {
        super.onCreate()
        Log.d(LIFECYCLE_TAG, "Unified battery notification service created (Android ${Build.VERSION.SDK_INT})")
        Log.d(LIFECYCLE_TAG, "Service state: foreground=${isForegroundServiceActive}, fallback=${isRunningInFallbackMode}")

        // Create notification channel for Android O+
        createNotificationChannel()

        // Register screen state receiver for battery optimization
        registerScreenStateReceiver()

        Log.d(LIFECYCLE_TAG, "Service onCreate completed successfully")
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(LIFECYCLE_TAG, "Service start command received - flags: $flags, startId: $startId")
        Log.d(LIFECYCLE_TAG, "Current monitoring state: $isMonitoring, foreground: $isForegroundServiceActive, fallback: $isRunningInFallbackMode")

        if (!isMonitoring) {
            Log.d(LIFECYCLE_TAG, "Starting monitoring for the first time")
            startMonitoring()
        } else {
            Log.d(LIFECYCLE_TAG, "Service already monitoring, ignoring start command")
        }

        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(LIFECYCLE_TAG, "Service destroyed - cleaning up resources")
        Log.d(LIFECYCLE_TAG, "Final state: monitoring=$isMonitoring, foreground=$isForegroundServiceActive, fallback=$isRunningInFallbackMode")

        isMonitoring = false
        isForegroundServiceActive = false
        isRunningInFallbackMode = false

        // Stop CoreBatteryStatsService
        stopCoreBatteryStatsService()

        // Clean up anti-theft handler
        antiThiefHandler?.removeCallbacks(antiThiefRunnable ?: return)
        antiThiefHandler = null
        antiThiefRunnable = null

        // Unregister screen state receiver
        unregisterScreenStateReceiver()

        Log.d(LIFECYCLE_TAG, "Service cleanup completed")
    }
    
    private fun startMonitoring() {
        Log.d(MONITORING_TAG, "Starting unified battery monitoring with CoreBatteryStatsService (Android ${Build.VERSION.SDK_INT})")
        Log.d(MONITORING_TAG, "Initial state: foreground=$isForegroundServiceActive, fallback=$isRunningInFallbackMode")
        isMonitoring = true

        // Start CoreBatteryStatsService for unified notifications
        startCoreBatteryStatsService()

        // Attempt to start as foreground service with comprehensive error handling
        if (!attemptForegroundServiceStart()) {
            Log.w(FALLBACK_TAG, "Foreground service start failed, continuing in fallback mode")
            isRunningInFallbackMode = true
        }

        // Start battery monitoring regardless of foreground service status
        startBatteryMonitoring()

        Log.d(MONITORING_TAG, "Monitoring startup completed - foreground=$isForegroundServiceActive, fallback=$isRunningInFallbackMode")
    }

    /**
     * Attempts to start the service as a foreground service with comprehensive error handling.
     * Returns true if successful, false if fallback mode should be used.
     */
    private fun attemptForegroundServiceStart(): Boolean {
        Log.d(FOREGROUND_TAG, "Attempting to start foreground service (Android ${Build.VERSION.SDK_INT})")
        return try {
            val minimalNotification = createMinimalServiceNotification()
            startForeground(NOTIFICATION_ID, minimalNotification)
            isForegroundServiceActive = true
            Log.d(FOREGROUND_TAG, "Successfully started as foreground service")
            true
        } catch (e: Exception) {
            handleForegroundServiceFailure(e)
            false
        }
    }

    /**
     * Handles foreground service startup failures with appropriate logging and fallback.
     */
    private fun handleForegroundServiceFailure(exception: Exception) {
        when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.S &&
            exception is ForegroundServiceStartNotAllowedException -> {
                Log.w(ERROR_TAG, "ForegroundServiceStartNotAllowedException: Android 12+ background restrictions")
                Log.w(FALLBACK_TAG, "Service will continue in fallback mode without foreground notification")
                Log.w(FALLBACK_TAG, "This is expected when app is started from background context")
            }
            else -> {
                Log.e(ERROR_TAG, "Unexpected error starting foreground service", exception)
                Log.w(FALLBACK_TAG, "Service will continue in fallback mode")
            }
        }
    }

    /**
     * Starts battery monitoring flow regardless of foreground service status.
     * This ensures core functionality continues even if notifications fail.
     */
    private fun startBatteryMonitoring() {
        serviceScope.launch {
            Log.d(MONITORING_TAG, "Starting CoreBatteryStatus flow collection")
            try {
                coreBatteryStatsProvider.coreBatteryStatusFlow
                    .filterNotNull()
                    .distinctUntilChanged { old, new ->
                        // Only process if something significant changed
                        val unchanged = old.isCharging == new.isCharging && old.percentage == new.percentage
                        if (unchanged) {
                            Log.v(MONITORING_TAG, "Skipping status update - no significant change")
                        }
                        unchanged
                    }
                    .onEach { status ->
                        Log.d(MONITORING_TAG, "New battery status: ${status.percentage}%, charging=${status.isCharging}, " +
                                "current=${status.currentMicroAmperes/1000}mA, voltage=${status.voltageMillivolts/1000}V")
                    }
                    .catch { e ->
                        Log.e(ERROR_TAG, "Error in battery status flow", e)
                    }
                    .collect { status ->
                        // Check for alarms and charging state changes
                        checkAlarmConditions(status)
                        checkChargingStateChange(status)

                        // Store last status for comparison
                        lastBatteryStatus = status
                    }
            } catch (e: Exception) {
                Log.e(ERROR_TAG, "Error in monitoring coroutine", e)
                // Continue running even if monitoring fails
                Log.w(FALLBACK_TAG, "Battery monitoring will retry automatically")
            }
        }
    }
    
    /**
     * Starts the CoreBatteryStatsService for unified battery monitoring notifications.
     */
    private fun startCoreBatteryStatsService() {
        if (!coreBatteryStatsServiceStarted) {
            Log.d(TAG, "Starting CoreBatteryStatsService for unified notifications")
            val intent = Intent(this, CoreBatteryStatsService::class.java)
            intent.action = CoreBatteryStatsService.ACTION_START_MONITORING
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                androidx.core.content.ContextCompat.startForegroundService(this, intent)
            } else {
                startService(intent)
            }
            coreBatteryStatsServiceStarted = true
        }
    }

    /**
     * Stops the CoreBatteryStatsService.
     */
    private fun stopCoreBatteryStatsService() {
        if (coreBatteryStatsServiceStarted) {
            Log.d(TAG, "Stopping CoreBatteryStatsService")
            val intent = Intent(this, CoreBatteryStatsService::class.java)
            intent.action = CoreBatteryStatsService.ACTION_STOP_MONITORING
            try {
                stopService(intent)
            } catch (e: Exception) {
                Log.e(TAG, "Error stopping CoreBatteryStatsService", e)
            }
            coreBatteryStatsServiceStarted = false
        }
    }

    /**
     * Creates the notification channel for unified battery monitoring on Android O+.
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Battery alarm service for charge targets and anti-theft monitoring"
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)

            Log.d(TAG, "Unified battery notification channel created: $CHANNEL_ID")
        }
    }

    /**
     * Creates a minimal service notification for this alarm service.
     * The main notification is handled by CoreBatteryStatsService.
     */
    private fun createMinimalServiceNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Battery Alarm Service")
            .setContentText("Monitoring for charge targets and alarms")
            .setSmallIcon(R.mipmap.ic_launcher)
            .setPriority(NotificationCompat.PRIORITY_MIN)
            .setOngoing(true)
            .setSilent(true)
            .build()
    }


    
    private fun checkAlarmConditions(status: CoreBatteryStatus) {
        val targetPercentage = appRepository.getChargeAlarmPercent()

        // Check if we've reached target percentage
        if (status.isCharging && targetPercentage > 0 &&
            status.percentage >= targetPercentage && !hasNotifiedTargetPercent) {
            Log.d(TAG, "Target charge percentage reached: ${status.percentage}%")
            showTargetChargeReachedNotification()
            hasNotifiedTargetPercent = true
        }

        // Check if fully charged
        if (status.isCharging && status.percentage >= 100 && !hasNotifiedFullCharge) {
            Log.d(TAG, "Battery fully charged: ${status.percentage}%")
            showFullChargeNotification()
            hasNotifiedFullCharge = true
        }

        // Reset flags when battery level drops
        if (status.percentage < lastBatteryLevel) {
            hasNotifiedTargetPercent = false
            hasNotifiedFullCharge = false
        }

        lastBatteryLevel = status.percentage
    }
    
    private fun checkChargingStateChange(status: CoreBatteryStatus) {
        if (status.isCharging != wasCharging) {
            Log.d(TAG, "Charging state changed: ${if (status.isCharging) "started" else "stopped"} charging")

            // Show charging state notifications if enabled
            if (status.isCharging && appRepository.isChargeNotificationEnabled()) {
                Log.d(TAG, "Showing charging started notification")
                showChargingStartedNotification()
            }

            if (!status.isCharging && appRepository.isDischargeNotificationEnabled()) {
                Log.d(TAG, "Showing charging stopped notification")
                showChargingStoppedNotification()
            }

            // Handle anti-theft logic
            handleAntiTheftLogic(status)

            wasCharging = status.isCharging
        }
    }
    
    private fun handleAntiTheftLogic(status: CoreBatteryStatus) {
        if (!status.isCharging && appRepository.isAntiThiefEnabled()) {
            Log.d(TAG, "Anti-theft enabled and charging stopped, starting delay timer")

            // Cancel any existing timer
            antiThiefHandler?.removeCallbacks(antiThiefRunnable ?: return)

            // Create new timer
            antiThiefHandler = android.os.Handler(mainLooper)
            antiThiefRunnable = Runnable {
                if (!AntiThiefUtils.isEnterPasswordActivityRunning(this@UnifiedBatteryNotificationService)) {
                    Log.d(TAG, "Anti-theft timer expired, launching EnterPasswordActivity")
                    val intent = Intent(this@UnifiedBatteryNotificationService, EnterPasswordActivity::class.java)
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    startActivity(intent)
                    appRepository.setAntiThiefAlertActive(true)
                }
            }

            antiThiefHandler?.postDelayed(antiThiefRunnable!!, ANTI_THEFT_DELAY_MS)
        } else if (status.isCharging) {
            // Cancel anti-theft timer if charging resumed
            antiThiefHandler?.removeCallbacks(antiThiefRunnable ?: return)
            appRepository.setAntiThiefAlertActive(false)
        }
    }

    /**
     * Shows a notification when the target charge percentage is reached.
     */
    private fun showTargetChargeReachedNotification() {
        sendSimpleNotification(
            "Charge Target Reached",
            "Please disconnect charger",
            2001
        )
    }

    /**
     * Shows a notification when the battery is fully charged.
     */
    private fun showFullChargeNotification() {
        sendSimpleNotification(
            "Battery Charged",
            "Battery is fully charged",
            2002
        )
    }

    /**
     * Shows a notification when charging starts.
     */
    private fun showChargingStartedNotification() {
        sendSimpleNotification(
            "Charging Started",
            "Device is now charging",
            2003
        )
    }

    /**
     * Shows a notification when charging stops.
     */
    private fun showChargingStoppedNotification() {
        sendSimpleNotification(
            "Charging Stopped",
            "Device is no longer charging",
            2004
        )
    }

    /**
     * Helper method to send simple notifications with fallback handling.
     * Works in both foreground service mode and fallback mode.
     */
    private fun sendSimpleNotification(title: String, message: String, notificationId: Int) {
        try {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager

            val pendingIntent = android.app.PendingIntent.getActivity(
                this,
                0,
                Intent(this, com.tqhit.battery.one.activity.main.MainActivity::class.java),
                android.app.PendingIntent.FLAG_IMMUTABLE
            )

            val notification = NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentTitle(title)
                .setContentText(message)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setAutoCancel(true)
                .setContentIntent(pendingIntent)
                .build()

            notificationManager.notify(notificationId, notification)

            if (isRunningInFallbackMode) {
                Log.d(TAG, "Notification sent in fallback mode: $title")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send notification: $title", e)
            // In fallback mode, we can still log the event for debugging
            Log.w(TAG, "NOTIFICATION_FALLBACK: $title - $message")
        }
    }

    // Screen state receiver for battery optimization
    private var screenStateReceiver: BroadcastReceiver? = null

    /**
     * Registers a receiver to monitor screen state changes for battery optimization.
     * Reduces notification update frequency when screen is off.
     */
    private fun registerScreenStateReceiver() {
        screenStateReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.action) {
                    Intent.ACTION_SCREEN_ON -> {
                        isScreenOn = true
                        Log.v(TAG, "Screen turned on - increasing notification update frequency")
                    }
                    Intent.ACTION_SCREEN_OFF -> {
                        isScreenOn = false
                        Log.v(TAG, "Screen turned off - reducing notification update frequency")
                    }
                }
            }
        }

        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_SCREEN_ON)
            addAction(Intent.ACTION_SCREEN_OFF)
        }

        try {
            registerReceiver(screenStateReceiver, filter)
            Log.d(TAG, "Screen state receiver registered")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to register screen state receiver", e)
        }
    }

    /**
     * Unregisters the screen state receiver to prevent memory leaks.
     */
    private fun unregisterScreenStateReceiver() {
        screenStateReceiver?.let { receiver ->
            try {
                unregisterReceiver(receiver)
                Log.d(TAG, "Screen state receiver unregistered")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to unregister screen state receiver", e)
            }
        }
        screenStateReceiver = null
    }
}
