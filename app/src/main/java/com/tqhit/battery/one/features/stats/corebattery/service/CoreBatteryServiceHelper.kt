package com.tqhit.battery.one.features.stats.corebattery.service

import android.app.ActivityManager
import android.app.ForegroundServiceStartNotAllowedException
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import androidx.core.content.ContextCompat
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Helper class for managing the CoreBatteryStatsService.
 * Provides centralized control for starting and stopping the service,
 * and checking its running status.
 */
@Singleton
class CoreBatteryServiceHelper @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "CoreBatteryServiceHelper"
    }
    
    /**
     * Checks if the CoreBatteryStatsService is currently running.
     *
     * @return true if the service is active, false otherwise
     */
    fun isServiceRunning(): Boolean {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        
        @Suppress("DEPRECATION")
        val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)
        
        val isRunning = runningServices.any { serviceInfo ->
            serviceInfo.service.className == CoreBatteryStatsService::class.java.name
        }
        
        BatteryLogger.d(TAG, "CoreBatteryStatsService running status: $isRunning")
        return isRunning
    }
    
    /**
     * Starts the CoreBatteryStatsService with monitoring action.
     * Handles foreground service start for Android O+ with comprehensive error handling
     * and permission checks for modern Android versions.
     */
    fun startService() {
        BatteryLogger.d(TAG, "Starting CoreBatteryStatsService")

        // Pre-flight checks for foreground service requirements
        if (!canStartForegroundService()) {
            BatteryLogger.w(TAG, "Cannot start foreground service due to missing requirements")
            // Still attempt to start as regular service for fallback
            startAsRegularService()
            return
        }

        val intent = Intent(context, CoreBatteryStatsService::class.java).apply {
            action = CoreBatteryStatsService.ACTION_START_MONITORING
        }

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Start as foreground service for Android O+ with enhanced error handling
                BatteryLogger.d(TAG, "Attempting to start CoreBatteryStatsService as foreground service")
                context.startForegroundService(intent)
                BatteryLogger.d(TAG, "CoreBatteryStatsService started as foreground service successfully")
            } else {
                // Start as regular service for older versions
                context.startService(intent)
                BatteryLogger.d(TAG, "CoreBatteryStatsService started as regular service")
            }
        } catch (e: Exception) {
            BatteryLogger.w(TAG, "Primary service startup failed, attempting fallback", e)
            handleServiceStartupFailure(e, intent)
        }
    }

    /**
     * Checks if the service can be started as a foreground service based on current conditions.
     *
     * @return true if foreground service startup requirements are met
     */
    private fun canStartForegroundService(): Boolean {
        // Check notification permission for Android 13+ (API 33+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(context, android.Manifest.permission.POST_NOTIFICATIONS)
                != PackageManager.PERMISSION_GRANTED) {
                BatteryLogger.w(TAG, "POST_NOTIFICATIONS permission not granted")
                return false
            }
        }

        return true
    }

    /**
     * Handles service startup failures with detailed logging and fallback mechanisms.
     * Specifically handles Android 12+ ForegroundServiceStartNotAllowedException.
     *
     * @param exception The exception that occurred during service startup
     * @param intent The intent used for service startup
     */
    private fun handleServiceStartupFailure(exception: Exception, intent: Intent) {
        when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.S &&
            exception is ForegroundServiceStartNotAllowedException -> {
                BatteryLogger.w(TAG, "ForegroundServiceStartNotAllowedException: Android 12+ background restrictions")
                BatteryLogger.w(TAG, "App likely not in foreground context when starting CoreBatteryStatsService")
                attemptFallbackServiceStart(intent, "Android 12+ background restrictions")
            }
            exception.message?.contains("ForegroundServiceStartNotAllowedException") == true -> {
                BatteryLogger.w(TAG, "ForegroundServiceStartNotAllowedException (string match): Background restrictions")
                BatteryLogger.w(TAG, "Cannot start foreground service from background context")
                attemptFallbackServiceStart(intent, "Background service restrictions")
            }
            exception.message?.contains("SecurityException") == true -> {
                BatteryLogger.e(TAG, "SecurityException: Missing required permissions", exception)
                attemptFallbackServiceStart(intent, "Missing permissions")
            }
            exception.message?.contains("IllegalStateException") == true -> {
                BatteryLogger.e(TAG, "IllegalStateException: Service in invalid state", exception)
                attemptFallbackServiceStart(intent, "Invalid service state")
            }
            else -> {
                BatteryLogger.e(TAG, "Unknown service startup error: ${exception.message}", exception)
                attemptFallbackServiceStart(intent, "Unknown error")
            }
        }
    }

    /**
     * Attempts to start the service using fallback mechanisms.
     */
    private fun attemptFallbackServiceStart(intent: Intent, reason: String) {
        BatteryLogger.d(TAG, "Attempting fallback service start due to: $reason")

        try {
            // Try to start as regular service (fallback)
            context.startService(intent)
            BatteryLogger.d(TAG, "CoreBatteryStatsService started as regular service (fallback mode)")
        } catch (fallbackException: Exception) {
            BatteryLogger.e(TAG, "Fallback service startup also failed", fallbackException)
            BatteryLogger.w(TAG, "CoreBatteryStatsService startup completely failed - battery monitoring may be limited")
        }
    }

    /**
     * Starts the service as a regular (non-foreground) service for fallback scenarios.
     */
    private fun startAsRegularService() {
        val intent = Intent(context, CoreBatteryStatsService::class.java).apply {
            action = CoreBatteryStatsService.ACTION_START_MONITORING
        }

        try {
            context.startService(intent)
            BatteryLogger.d(TAG, "CoreBatteryStatsService started as regular service (fallback mode)")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to start service even in fallback mode", e)
        }
    }
    
    /**
     * Stops the CoreBatteryStatsService by sending a stop monitoring action.
     * Logs the action for debugging purposes.
     */
    fun stopService() {
        BatteryLogger.d(TAG, "Stopping CoreBatteryStatsService")
        
        val intent = Intent(context, CoreBatteryStatsService::class.java).apply {
            action = CoreBatteryStatsService.ACTION_STOP_MONITORING
        }
        
        try {
            context.startService(intent)
            BatteryLogger.d(TAG, "Stop command sent to CoreBatteryStatsService")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to stop CoreBatteryStatsService", e)
        }
    }
    
    /**
     * Restarts the CoreBatteryStatsService by stopping and then starting it.
     * Useful for applying configuration changes or recovering from errors.
     */
    fun restartService() {
        BatteryLogger.d(TAG, "Restarting CoreBatteryStatsService")
        stopService()

        // Small delay to ensure service stops before restarting
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            startService()
        }, 1000)
    }

    /**
     * Gets detailed service status information for debugging and monitoring.
     *
     * @return Map containing service status and configuration details
     */
    fun getServiceStatus(): Map<String, Any> {
        val serviceInstance = CoreBatteryStatsService.getInstance()
        val baseStatus = mapOf(
            "isServiceRunning" to isServiceRunning(),
            "androidApiLevel" to Build.VERSION.SDK_INT,
            "hasNotificationPermission" to (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU ||
                ContextCompat.checkSelfPermission(context, android.Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED),
            "canStartForegroundService" to canStartForegroundService()
        )

        return if (serviceInstance != null) {
            baseStatus + serviceInstance.getServiceState()
        } else {
            baseStatus + mapOf("serviceInstanceAvailable" to false)
        }
    }

    /**
     * Attempts to recover the service if it's not running properly.
     * This method tries to restart the service or recover from foreground service failures.
     *
     * @return true if recovery was attempted, false if service is already healthy
     */
    fun attemptServiceRecovery(): Boolean {
        BatteryLogger.d(TAG, "Attempting service recovery")

        val serviceInstance = CoreBatteryStatsService.getInstance()

        if (serviceInstance == null) {
            BatteryLogger.d(TAG, "Service instance not available, attempting restart")
            startService()
            return true
        }

        if (!serviceInstance.isMonitoringActive()) {
            BatteryLogger.d(TAG, "Service monitoring not active, attempting recovery")
            serviceInstance.attemptServiceRecovery()
            return true
        }

        BatteryLogger.d(TAG, "Service appears healthy, no recovery needed")
        return false
    }

    /**
     * Gets a user-friendly status message about the service.
     *
     * @return Human-readable status message
     */
    fun getServiceStatusMessage(): String {
        val serviceInstance = CoreBatteryStatsService.getInstance()
        return if (serviceInstance != null) {
            serviceInstance.getServiceStatusMessage()
        } else if (isServiceRunning()) {
            "Battery monitoring service starting..."
        } else {
            "Battery monitoring service not running"
        }
    }

    /**
     * Checks if the service is providing core monitoring functionality.
     *
     * @return true if battery monitoring is active
     */
    fun isMonitoringActive(): Boolean {
        val serviceInstance = CoreBatteryStatsService.getInstance()
        return serviceInstance?.isMonitoringActive() ?: false
    }
}
